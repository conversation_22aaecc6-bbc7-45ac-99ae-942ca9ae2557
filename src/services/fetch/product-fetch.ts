import { sdk } from "@/libs/medusaClient";
import { getStrapiProductDetails } from "@/libs/strapiApis";
import { FetchResult } from "@/types/Common";
import { ProductDetailsType } from "@/types/Collections/ProductDetails";
import { getBYOBProductDetailsQuery } from "@/graphql/pageQuery";
import strapiGraphqlFetch from "@/utils/strapiFetch";
import { MedusaBundleData } from "@/components/Partials/PDP/types";

interface FetchProductDataParams {
  productHandle?: string;
}

// Types for BYOB bundle data
interface BundleVariant {
  product_id: string;
  quantity: number;
  [key: string]: any;
}

interface BundleData {
  data: MedusaBundleData;
  [key: string]: any;
}

// Types for bundle product data structure (BYOB and COMBO)
interface BYOBProductData {
  strapiProduct: ProductDetailsType;
  medusaProduct: any;
  bundleVariants: ProductDetailsType[];
  medusaBundleData: MedusaBundleData; // Add Medusa bundle data
  productType: "BYOB" | "COMBO";
}

interface RegularProductData {
  strapiProduct: ProductDetailsType;
  medusaProduct: any;
  productType: "VARIANT";
}

/**
 * Fetch multiple Strapi products by their systemIds
 */
async function fetchStrapiProductsByIds(
  systemIds: string[]
): Promise<ProductDetailsType[]> {
  try {
    const result = await strapiGraphqlFetch({
      query: getBYOBProductDetailsQuery,
      variables: {
        bundleFilters: {
          systemId: {
            in: systemIds,
          },
        },
        mainProductFilters: {
          systemId: {
            in: [], // Empty array since we only need bundle products
          },
        },
      },
    });

    return result.bundle || [];
  } catch (error) {
    console.error("Error fetching Strapi products by IDs:", error);
    return [];
  }
}

export async function fetchProductData({
  productHandle,
}: FetchProductDataParams): Promise<FetchResult> {
  try {
    // Here first i neeed to fetch the product from medusa by handle
    const medusaProductByHandle = await sdk.store.product.list({
      handle: productHandle,
      fields:
        "+variants.calculated_price.*,+variants.extended_product_variants.*,cms_product.*,+variants.prices.*,+variants.variant_image.*,+categories.*",
      region_id: "reg_01JWBD587RMPB20Y9ARTGJMT2D",
    });

    console.log("medusaProductByHandle: ", medusaProductByHandle);

    const checkProductType = medusaProductByHandle.products[0].type?.value;
    console.log("checkProductType: ", checkProductType);

    enum ProductType {
      VARIANT = "VARIANT",
      BYOB = "BYOB",
      COMBO = "COMBO",
    }

    if (
      checkProductType === ProductType.BYOB ||
      checkProductType === ProductType.COMBO
    ) {
      const variantId =
        medusaProductByHandle?.products &&
        medusaProductByHandle.products[0] &&
        medusaProductByHandle.products[0].variants &&
        medusaProductByHandle.products[0].variants[0]
          ? medusaProductByHandle.products[0].variants[0].id
          : undefined;

      console.log("variantId: ", variantId);

      if (!variantId) {
        return {
          success: false,
          error: "Variant ID not found for bundle product",
        };
      }

      // Fetch bundle data from Medusa
      const byobProduct = await sdk.client.fetch(
        `/store/bundles/${variantId}`,
        {
          method: "GET",
          cache: "force-cache",
        }
      );

      const bundleData = byobProduct as BundleData;
      console.log("byobProduct: ", bundleData.data.bundle);

      // Extract Strapi IDs from bundle variants
      const strapiIds = bundleData.data.bundle.variants.map(
        (variant: BundleVariant) => variant.product_id
      );

      console.log("strapiIds: ", strapiIds);

      // Fetch the main bundle product from Strapi
      const strapiProduct = await getStrapiProductDetails({ productHandle });

      if (!strapiProduct) {
        return { success: false, error: "Bundle product not found in Strapi" };
      }

      // Fetch all bundle variant products from Strapi
      const bundleVariants = await fetchStrapiProductsByIds(strapiIds);

      if (bundleVariants.length === 0) {
        return {
          success: false,
          error: "Bundle variant products not found in Strapi",
        };
      }

      const typedStrapiProduct = strapiProduct as unknown as ProductDetailsType;

      // Return bundle product data structure (BYOB or COMBO)
      return {
        success: true,
        data: {
          strapiProduct: typedStrapiProduct,
          medusaProduct: medusaProductByHandle.products[0],
          bundleVariants,
          medusaBundleData: bundleData.data, // Include Medusa bundle data
          productType: checkProductType as "BYOB" | "COMBO",
        } as BYOBProductData,
      };
    }

    // Handle regular VARIANT products
    const strapiProduct = await getStrapiProductDetails({ productHandle });

    if (!strapiProduct) {
      return { success: false, error: "Product not found in Strapi" };
    }

    const typedStrapiProduct = strapiProduct as unknown as ProductDetailsType;

    // Get the Medusa product ID from Strapi product
    const medusaProductId = typedStrapiProduct.systemId as string;

    if (!medusaProductId) {
      return {
        success: false,
        error: "Product ID not found for Medusa",
      };
    }

    // Return regular product data structure
    return {
      success: true,
      data: {
        strapiProduct: typedStrapiProduct,
        medusaProduct: medusaProductByHandle.products[0],
        productType: "VARIANT",
      } as RegularProductData,
    };
  } catch (error) {
    console.error("Error in fetchProductData:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? `Failed to fetch product data: ${error.message}`
          : "An unknown error occurred while fetching product data",
    };
  }
}
