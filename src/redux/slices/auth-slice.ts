/* eslint-disable @typescript-eslint/no-unused-vars */
import { createSlice, PayloadAction } from "@reduxjs/toolkit";

export interface AuthState {
  isAuthenticated: boolean;
  user: {
    id?: string;
    name?: string;
    email?: string;
    phone?: string;
  } | null;
  loading: boolean;
  error: string | null;
  otpSent: boolean;
  otpVerified: boolean;
}

const initialState: AuthState = {
  isAuthenticated: false,
  user: null,
  loading: false,
  error: null,
  otpSent: false,
  otpVerified: false,
};

export const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    // Login actions
    loginRequest: (state, action: PayloadAction<{ phone: string }>) => {
      state.loading = true;
      state.error = null;
      state.otpSent = false;
    },
    loginSuccess: (
      state,
      action: PayloadAction<{ user: AuthState["user"] }>
    ) => {
      state.isAuthenticated = true;
      state.user = action.payload.user;
      state.loading = false;
      state.error = null;
    },
    loginFailure: (state, action: PayloadAction<{ error: string }>) => {
      state.loading = false;
      state.error = action.payload.error;
    },

    // OTP actions
    sendOtpRequest: (state, action: PayloadAction<{ phone: string }>) => {
      state.loading = true;
      state.error = null;
    },
    sendOtpSuccess: (state) => {
      state.loading = false;
      state.otpSent = true;
      state.error = null;
    },
    sendOtpFailure: (state, action: PayloadAction<{ error: string }>) => {
      state.loading = false;
      state.error = action.payload.error;
      state.otpSent = false;
    },

    // Verify OTP actions
    verifyOtpRequest: (
      state,
      action: PayloadAction<{ phone: string; otp: string }>
    ) => {
      state.loading = true;
      state.error = null;
    },
    verifyOtpSuccess: (
      state,
      action: PayloadAction<{ user: AuthState["user"] }>
    ) => {
      state.loading = false;
      state.otpVerified = true;
      state.isAuthenticated = true;
      state.user = action.payload.user;
      state.error = null;
    },
    verifyOtpFailure: (state, action: PayloadAction<{ error: string }>) => {
      state.loading = false;
      state.error = action.payload.error;
      state.otpVerified = false;
    },

    // Logout action
    logout: (state) => {
      state.isAuthenticated = false;
      state.user = null;
      state.otpSent = false;
      state.otpVerified = false;
    },

    // Reset state
    resetAuthState: (state) => {
      state.loading = false;
      state.error = null;
      state.otpSent = false;
      state.otpVerified = false;
    },
  },
});

export const {
  loginRequest,
  loginSuccess,
  loginFailure,
  sendOtpRequest,
  sendOtpSuccess,
  sendOtpFailure,
  verifyOtpRequest,
  verifyOtpSuccess,
  verifyOtpFailure,
  logout,
  resetAuthState,
} = authSlice.actions;

export default authSlice.reducer;
