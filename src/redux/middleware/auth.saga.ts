/* eslint-disable @typescript-eslint/no-unused-vars */
import { call, put, takeLatest } from "redux-saga/effects";
import { PayloadAction } from "@reduxjs/toolkit";
import {
  AUTH_SEND_OTP,
  AUTH_VERIFY_OTP,
  AUTH_LOGIN,
  AUTH_LOGOUT,
  sendOtpRequest,
  sendOtpSuccess,
  sendOtpFailure,
  verifyOtpRequest,
  verifyOtpSuccess,
  verifyOtpFailure,
  loginRequest,
  loginSuccess,
  loginFailure,
  logout,
} from "../actions/auth.actions";

// Mock API calls - replace with actual API calls
const sendOtpApi = async (phone: string) => {
  // Simulate API call
  return new Promise<{ success: boolean }>((resolve) => {
    setTimeout(() => {
      resolve({ success: true });
    }, 1000);
  });
};

const verifyOtpApi = async (phone: string, otp: string) => {
  // Simulate API call
  return new Promise<{ success: boolean; user: any }>((resolve) => {
    setTimeout(() => {
      if (otp === "123456") {
        // For testing purposes
        resolve({
          success: true,
          user: {
            id: "1",
            name: "Test User",
            phone: phone,
          },
        });
      } else {
        throw new Error("Invalid OTP");
      }
    }, 1000);
  });
};

// Saga workers
function* sendOtpSaga(action: PayloadAction<{ phone: string }>) {
  try {
    yield put(sendOtpRequest(action.payload));
    const response: { success: boolean } = yield call(
      sendOtpApi,
      action.payload.phone
    );

    if (response.success) {
      yield put(sendOtpSuccess());
    } else {
      yield put(sendOtpFailure({ error: "Failed to send OTP" }));
    }
  } catch (error: any) {
    yield put(sendOtpFailure({ error: error.message || "Failed to send OTP" }));
  }
}

function* verifyOtpSaga(action: PayloadAction<{ phone: string; otp: string }>) {
  try {
    yield put(verifyOtpRequest(action.payload));
    const response: { success: boolean; user: any } = yield call(
      verifyOtpApi,
      action.payload.phone,
      action.payload.otp
    );

    if (response.success) {
      yield put(verifyOtpSuccess({ user: response.user }));
    } else {
      yield put(verifyOtpFailure({ error: "Failed to verify OTP" }));
    }
  } catch (error: any) {
    yield put(
      verifyOtpFailure({ error: error.message || "Failed to verify OTP" })
    );
  }
}

function* loginSaga(action: PayloadAction<{ phone: string }>) {
  try {
    yield put(loginRequest(action.payload));
    // In this flow, login is just sending OTP
    const response: { success: boolean } = yield call(
      sendOtpApi,
      action.payload.phone
    );

    if (response.success) {
      yield put(sendOtpSuccess());
    } else {
      yield put(loginFailure({ error: "Failed to login" }));
    }
  } catch (error: any) {
    yield put(loginFailure({ error: error.message || "Failed to login" }));
  }
}

function* logoutSaga() {
  try {
    // Any cleanup needed before logout
    yield put(logout());
    // Could add additional actions like clearing local storage, etc.
  } catch (error: any) {
    console.error("Logout error:", error);
  }
}

// Saga watchers
export function* authSaga() {
  yield takeLatest(AUTH_SEND_OTP, sendOtpSaga);
  yield takeLatest(AUTH_VERIFY_OTP, verifyOtpSaga);
  yield takeLatest(AUTH_LOGIN, loginSaga);
  yield takeLatest(AUTH_LOGOUT, logoutSaga);
}
