"use client";
import React from "react";
import {
  Dialog,
  DialogContent,
  DialogOverlay,
  DialogPortal,
  DialogClose,
} from "@/components/ui/dialog";
import { X } from "lucide-react";
import Image from "next/image";
import { Product } from "@/components/Common/ProductCard";
import { calculateDiscount } from "@/utils/pricing";
import WhiteUnderline from "@/assets/icons/WhiteUnderline";

interface CouponOffer {
  title: string;
  description: string;
  code: string;
  minOrderValue: number;
  discountText: string;
}

interface PriceSummaryModalProps {
  isOpen: boolean;
  onClose: () => void;
  product: Product;
  onAddToCart: (product: Product) => void;
}

const PriceSummaryModal: React.FC<PriceSummaryModalProps> = ({
  isOpen,
  onClose,
  product,
  onAddToCart,
}) => {
  const primaryColor = product.primaryColor || "#036A38";
  const backgroundColor = "#EFD8E0"; // Light background for modal content

  // Sample coupon offers - in real app, this would come from props or API
  const couponOffers: CouponOffer[] = [
    {
      title:
        "Get 10% off on item total above ₹14,500. Applicable Only on online payment",
      description: "Get 10% off on item total above ₹14,500.",
      code: "WHEYMORE",
      minOrderValue: 4500,
      discountText: "10% off",
    },
    {
      title: "Get 10% off on item total above ₹600.",
      description: "Get 10% off on item total above ₹600.",
      code: "LIGHTEST",
      minOrderValue: 600,
      discountText: "10% off",
    },
    {
      title: "Get 10% off on item total above ₹1.",
      description: "Get 10% off on item total above ₹1.",
      code: "FIRSTTIME",
      minOrderValue: 1,
      discountText: "10% off",
    },
  ];

  const handleCopyCode = (code: string) => {
    navigator.clipboard.writeText(code);
    // You could add a toast notification here
  };

  const handleAddToCart = () => {
    onAddToCart(product);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogPortal>
        <DialogOverlay className="!z-[9998] bg-black/60 gap-0" />
        <DialogContent
          style={{ backgroundColor: backgroundColor }}
          className="max-w-[500px] w-full max-h-[90vh] p-0 border-none overflow-hidden !z-[9999] [&>button:not([data-custom-close])]:hidden flex flex-col gap-0"
        >
          <DialogClose
            data-custom-close
            className="absolute right-4 top-4 !z-[60] rounded-full bg-white/90 hover:bg-white p-2 shadow-lg transition-all duration-200"
            onClick={onClose}
          >
            <X className="h-6 w-6 text-black" />
            <span className="sr-only">Close</span>
          </DialogClose>

          {/* Fixed Header Section */}
          <div
            className="px-7.5 py-7.5 text-white flex-shrink-0"
            style={{
              background: `${primaryColor}`,
            }}
          >
            <div>
              <p className="text-[34px] text-white font-bold font-narrow mb-1">
                Price summary
              </p>
              <WhiteUnderline width={200} height={7} />
            </div>

            {/* Product Info */}
            <div className="flex  items-center mt-5">
              <div className="relative w-30 h-30 flex-shrink-0 rounded-[10px]">
                <Image
                  src={product.image}
                  alt={product.title}
                  fill
                  objectFit="contain"
                  className="w-full h-full object-contain rounded-[10px]"
                />
              </div>
              <div className="flex-1">
                <h3 className="text-lg font-semibold font-obviously leading-6 mb-2">
                  {product.title}
                </h3>
              </div>
            </div>
          </div>

          {/* Scrollable Content Section */}
          <div
            className="flex-1 overflow-y-auto"
            style={
              {
                // maxHeight: "calc(90vh - 200px)", // Subtract approximate header height
              }
            }
          >
            {/* Pricing Section */}
            <div className="px-7.5 py-2.5" style={{ backgroundColor }}>
              <div
                className="bg-transparent rounded-lg
              "
              >
                <div className="flex justify-between items-center pb-2.5 border-b border-b-[#BFADB3]">
                  <h5 className="text-xl leading-7 font-[510] font-narrow">
                    MRP
                  </h5>
                </div>
                <div className="flex justify-between items-center pb-2.5 border-b border-b-[#BFADB3]">
                  <h5 className="text-xl leading-7 font-[510] font-narrow">
                    Regular Price
                  </h5>
                </div>

                <div className="flex justify-between items-center mb-2">
                  <span className="text-base font-obviously">
                    Regular Price
                  </span>
                  <div className="flex items-center gap-2">
                    <span className="text-lg font-semibold">
                      ₹
                      {(
                        product.originalPrice || product.price
                      )?.toLocaleString()}
                    </span>
                    {product.originalPrice && (
                      <span
                        className="text-sm px-2 py-1 rounded"
                        style={{
                          color: primaryColor,
                          backgroundColor: `${primaryColor}20`,
                        }}
                      >
                        (
                        {calculateDiscount(
                          product.originalPrice,
                          product.price
                        )}
                        % OFF)
                      </span>
                    )}
                  </div>
                </div>

                <div className="flex justify-between items-center border-t pt-2">
                  <span
                    className="text-lg font-bold font-obviously"
                    style={{ color: primaryColor }}
                  >
                    Offer Price
                  </span>
                  <div className="flex items-center gap-2">
                    <span
                      className="text-xl font-bold"
                      style={{ color: primaryColor }}
                    >
                      ₹{product.price?.toLocaleString()}
                    </span>
                    {product.originalPrice && (
                      <span
                        className="text-sm px-2 py-1 rounded font-semibold"
                        style={{
                          color: primaryColor,
                          backgroundColor: `${primaryColor}20`,
                        }}
                      >
                        (
                        {calculateDiscount(
                          product.originalPrice,
                          product.price
                        )}
                        % OFF)
                      </span>
                    )}
                  </div>
                </div>
              </div>

              {/* Save more with offers */}
              <div className="bg-white rounded-lg p-4">
                <h3 className="text-lg font-bold font-obviously mb-4">
                  Save more with offers
                </h3>

                <div className="space-y-3">
                  {couponOffers.map((offer, index) => (
                    <div key={index} className="border rounded-lg p-4">
                      <p className="text-sm font-medium mb-2">{offer.title}</p>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <span className="text-xs">Coupon code</span>
                          <div className="flex items-center gap-2">
                            <code
                              className="px-2 py-1 rounded text-sm font-mono font-semibold"
                              style={{
                                backgroundColor: primaryColor,
                                color: "white",
                              }}
                            >
                              {offer.code}
                            </code>
                            <button
                              onClick={() => handleCopyCode(offer.code)}
                              className="p-1 hover:bg-gray-100 rounded"
                            >
                              <svg
                                width="16"
                                height="16"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="2"
                              >
                                <rect
                                  x="9"
                                  y="9"
                                  width="13"
                                  height="13"
                                  rx="2"
                                  ry="2"
                                ></rect>
                                <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                              </svg>
                            </button>
                          </div>
                        </div>
                        <button
                          onClick={handleAddToCart}
                          className="px-4 py-2 rounded text-sm font-semibold text-white transition-colors"
                          style={{ backgroundColor: primaryColor }}
                        >
                          ADD TO CART
                        </button>
                      </div>
                      <p className="text-xs text-gray-600 mt-1">
                        Minimum order value: ₹
                        {offer.minOrderValue?.toLocaleString()}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </DialogContent>
      </DialogPortal>
    </Dialog>
  );
};

export default PriceSummaryModal;
