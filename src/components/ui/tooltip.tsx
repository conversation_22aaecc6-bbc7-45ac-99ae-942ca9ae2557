"use client";

import * as React from "react";
import * as TooltipPrimitive from "@radix-ui/react-tooltip";
import { cn } from "@/libs/utils";

function TooltipProvider({
  delayDuration = 0,
  ...props
}: React.ComponentProps<typeof TooltipPrimitive.Provider>) {
  return (
    <TooltipPrimitive.Provider
      data-slot="tooltip-provider"
      delayDuration={delayDuration}
      {...props}
    />
  );
}

function Tooltip({
  ...props
}: React.ComponentProps<typeof TooltipPrimitive.Root>) {
  return (
    <TooltipProvider>
      <TooltipPrimitive.Root data-slot="tooltip" {...props} />
    </TooltipProvider>
  );
}

function TooltipTrigger({
  ...props
}: React.ComponentProps<typeof TooltipPrimitive.Trigger>) {
  return <TooltipPrimitive.Trigger data-slot="tooltip-trigger" {...props} />;
}

function TooltipContent({
  className,
  sideOffset = 0,
  children,
  ...props
}: React.ComponentProps<typeof TooltipPrimitive.Content>) {
  return (
    <TooltipPrimitive.Portal>
      <TooltipPrimitive.Content
        data-slot="tooltip-content"
        sideOffset={sideOffset}
        className={cn(
          "bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",
          className
        )}
        {...props}
      >
        {children}
        <TooltipPrimitive.Arrow className="bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]" />
      </TooltipPrimitive.Content>
    </TooltipPrimitive.Portal>
  );
}

// Mobile-friendly tooltip that supports both hover and click interactions
interface MobileTooltipProps {
  children: React.ReactNode;
  content: React.ReactNode;
  className?: string;
  contentClassName?: string;
  sideOffset?: number;
}

function MobileTooltip({
  children,
  content,
  className,
  contentClassName,
  sideOffset = 4,
}: MobileTooltipProps) {
  const [isOpen, setIsOpen] = React.useState(false);
  const [isMobile, setIsMobile] = React.useState(false);
  const [tooltipPosition, setTooltipPosition] = React.useState<{
    alignment: 'left' | 'center' | 'right';
  }>({ alignment: 'center' });
  const triggerRef = React.useRef<HTMLDivElement>(null);
  const tooltipRef = React.useRef<HTMLDivElement>(null);

  // Detect if device supports touch (mobile/tablet)
  React.useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile('ontouchstart' in window || navigator.maxTouchPoints > 0);
    };

    checkIsMobile();
    window.addEventListener('resize', checkIsMobile);

    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);

  // Calculate optimal tooltip position to avoid viewport overflow
  const calculateTooltipPosition = React.useCallback(() => {
    if (!triggerRef.current || !isMobile) return;

    const triggerRect = triggerRef.current.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const tooltipWidth = 200; // max-w-[200px] from our styling
    const padding = 16; // padding from viewport edges

    // Determine horizontal alignment
    const triggerCenter = triggerRect.left + triggerRect.width / 2;
    const tooltipHalfWidth = tooltipWidth / 2;

    let alignment: 'left' | 'center' | 'right' = 'center';

    // Check if centered tooltip would overflow
    if (triggerCenter - tooltipHalfWidth < padding) {
      alignment = 'left';
    } else if (triggerCenter + tooltipHalfWidth > viewportWidth - padding) {
      alignment = 'right';
    }

    // Additional safety check for very narrow screens
    if (viewportWidth < tooltipWidth + (padding * 2)) {
      alignment = 'center'; // Force center and let CSS handle overflow with max-width
    }

    setTooltipPosition({ alignment });
  }, [isMobile, sideOffset]);

  // Recalculate position when tooltip opens or viewport changes
  React.useEffect(() => {
    if (isOpen && isMobile) {
      calculateTooltipPosition();

      // Recalculate on window resize/orientation change
      const handleResize = () => {
        calculateTooltipPosition();
      };

      window.addEventListener('resize', handleResize);
      window.addEventListener('orientationchange', handleResize);

      return () => {
        window.removeEventListener('resize', handleResize);
        window.removeEventListener('orientationchange', handleResize);
      };
    }
  }, [isOpen, isMobile, calculateTooltipPosition]);

  // Close tooltip when clicking outside on mobile
  React.useEffect(() => {
    if (!isMobile || !isOpen) return;

    const handleClickOutside = (event: MouseEvent | TouchEvent) => {
      const target = event.target as Element;
      if (!target.closest('[data-mobile-tooltip]')) {
        setIsOpen(false);
      }
    };

    document.addEventListener('click', handleClickOutside);
    document.addEventListener('touchstart', handleClickOutside);

    return () => {
      document.removeEventListener('click', handleClickOutside);
      document.removeEventListener('touchstart', handleClickOutside);
    };
  }, [isMobile, isOpen]);

  if (isMobile) {
    // Mobile: Use click/tap interaction with smart positioning
    const getTooltipClasses = () => {
      const baseClasses = "absolute z-50 w-max max-w-[200px] min-w-[120px] rounded-md bg-primary text-primary-foreground px-3 py-1.5 text-xs animate-in fade-in-0 zoom-in-95 break-words";

      // Always position on top (bottom-full means above the trigger)
      const positionClasses = "bottom-full";

      // Alignment classes with viewport-aware positioning
      let alignmentClasses = "";
      switch (tooltipPosition.alignment) {
        case 'left':
          alignmentClasses = "left-0";
          break;
        case 'right':
          alignmentClasses = "right-0";
          break;
        case 'center':
        default:
          alignmentClasses = "left-1/2 transform -translate-x-1/2";
          break;
      }

      // Add responsive max-width for very small screens
      const responsiveClasses = "max-w-[calc(100vw-2rem)] sm:max-w-[200px]";

      return cn(baseClasses, positionClasses, alignmentClasses, responsiveClasses, contentClassName);
    };

    const getArrowClasses = () => {
      const baseClasses = "absolute w-0 h-0";

      // Arrow always pointing down (tooltip is always above)
      const borderClasses = "border-l-[6px] border-r-[6px] border-t-[6px] border-l-transparent border-r-transparent border-t-primary";
      const positionClasses = "top-full";

      let alignmentClasses = "";
      switch (tooltipPosition.alignment) {
        case 'left':
          alignmentClasses = "left-4";
          break;
        case 'right':
          alignmentClasses = "right-4";
          break;
        case 'center':
        default:
          alignmentClasses = "left-1/2 transform -translate-x-1/2";
          break;
      }

      return cn(baseClasses, borderClasses, positionClasses, alignmentClasses);
    };

    const getTooltipStyle = () => {
      // Always use bottom margin since tooltip is always on top
      return { marginBottom: `${sideOffset}px` };
    };

    return (
      <div
        ref={triggerRef}
        className={cn("relative inline-block", className)}
        data-mobile-tooltip
      >
        <button
          type="button"
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            setIsOpen(!isOpen);
          }}
          className="cursor-pointer touch-manipulation"
          aria-label="Show tooltip"
        >
          {children}
        </button>
        {isOpen && (
          <div
            ref={tooltipRef}
            className={getTooltipClasses()}
            style={getTooltipStyle()}
          >
            {content}
            <div className={getArrowClasses()} />
          </div>
        )}
      </div>
    );
  }

  // Desktop: Use hover interaction with Radix UI
  return (
    <TooltipProvider>
      <TooltipPrimitive.Root>
        <TooltipPrimitive.Trigger asChild className={className}>
          <div className="cursor-pointer">
            {children}
          </div>
        </TooltipPrimitive.Trigger>
        <TooltipPrimitive.Portal>
          <TooltipPrimitive.Content
            side="top"
            sideOffset={sideOffset}
            className={cn(
              "bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",
              contentClassName
            )}
          >
            {content}
            <TooltipPrimitive.Arrow className="bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]" />
          </TooltipPrimitive.Content>
        </TooltipPrimitive.Portal>
      </TooltipPrimitive.Root>
    </TooltipProvider>
  );
}

export { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider, MobileTooltip };
