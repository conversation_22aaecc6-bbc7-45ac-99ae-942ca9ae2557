"use client";

import React, { useState, useRef, useCallback, useEffect } from "react";
import Img from "@/components/Elements/img";
import {
  Carousel,
  CarouselContent,
  CarouselDots,
  CarouselItem,
  type CarouselApi,
} from "@/components/ui/carousel";
import { ChevronLeft, ChevronRight, X } from "lucide-react";
import { ShareIcon } from "@/assets/icons/ShareIcon";
import { useShare } from "@/hooks/useShare";

interface MobileProductCarouselProps {
  images: string[];
  title?: string;
  currentSlide: number;
  onSlideChange: (index: number) => void;
  primaryColor?: string;
}

export default function MobileProductCarousel({
  images,
  title,
  currentSlide,
  onSlideChange,
  primaryColor,
}: MobileProductCarouselProps) {
  // Note: primaryColor prop is available in the interface but not currently used in mobile carousel
  const [carouselApi, setCarouselApi] = useState<CarouselApi>();
  const [modalOpen, setModalOpen] = useState(false);
  const [modalCurrentSlide, setModalCurrentSlide] = useState(0);

  const modalThumbnailContainerRef = useRef<HTMLDivElement>(null);
  const mainThumbnailContainerRef = useRef<HTMLDivElement>(null);

  // Touch/swipe handling for modal
  const touchStartRef = useRef<{ x: number; y: number; time: number } | null>(
    null
  );
  const modalImageRef = useRef<HTMLDivElement>(null);

  // Zoom and pan state
  const [zoomState, setZoomState] = useState({
    scale: 1,
    translateX: 0,
    translateY: 0,
  });

  // Touch handling for pinch-to-zoom
  const lastTouchDistance = useRef<number>(0);
  const lastTouchCenter = useRef<{ x: number; y: number }>({ x: 0, y: 0 });
  const isPinching = useRef<boolean>(false);
  const panStartRef = useRef<{
    x: number;
    y: number;
    translateX: number;
    translateY: number;
  } | null>(null);

  // Utility functions for zoom and pan
  const getTouchDistance = useCallback((touches: React.TouchList) => {
    if (touches.length < 2) return 0;
    const touch1 = touches[0];
    const touch2 = touches[1];
    return Math.sqrt(
      Math.pow(touch2.clientX - touch1.clientX, 2) +
        Math.pow(touch2.clientY - touch1.clientY, 2)
    );
  }, []);

  const getTouchCenter = useCallback((touches: React.TouchList) => {
    if (touches.length < 2) return { x: 0, y: 0 };
    const touch1 = touches[0];
    const touch2 = touches[1];
    return {
      x: (touch1.clientX + touch2.clientX) / 2,
      y: (touch1.clientY + touch2.clientY) / 2,
    };
  }, []);

  const constrainPan = useCallback(
    (translateX: number, translateY: number, scale: number) => {
      if (!modalImageRef.current) return { translateX, translateY };

      const container = modalImageRef.current;
      const containerRect = container.getBoundingClientRect();
      const scaledWidth = containerRect.width * scale;
      const scaledHeight = containerRect.height * scale;

      // Calculate maximum allowed translation
      const maxTranslateX = Math.max(
        0,
        (scaledWidth - containerRect.width) / 2
      );
      const maxTranslateY = Math.max(
        0,
        (scaledHeight - containerRect.height) / 2
      );

      return {
        translateX: Math.max(
          -maxTranslateX,
          Math.min(maxTranslateX, translateX)
        ),
        translateY: Math.max(
          -maxTranslateY,
          Math.min(maxTranslateY, translateY)
        ),
      };
    },
    []
  );

  const resetZoom = useCallback(() => {
    setZoomState({
      scale: 1,
      translateX: 0,
      translateY: 0,
    });
  }, []);

  // Double-tap to zoom functionality
  const lastTapRef = useRef<number>(0);
  const handleDoubleTap = useCallback(
    (e: React.TouchEvent) => {
      const now = Date.now();
      const timeDiff = now - lastTapRef.current;

      if (timeDiff < 300 && timeDiff > 0) {
        // Double tap detected
        e.preventDefault();

        if (zoomState.scale === 1) {
          // Zoom in to 2x at the tap location
          const touch = e.changedTouches[0];
          const containerRect = modalImageRef.current?.getBoundingClientRect();

          if (containerRect) {
            const centerX =
              touch.clientX - containerRect.left - containerRect.width / 2;
            const centerY =
              touch.clientY - containerRect.top - containerRect.height / 2;

            const newScale = 2;
            const newTranslateX = (-centerX * (newScale - 1)) / newScale;
            const newTranslateY = (-centerY * (newScale - 1)) / newScale;

            const constrained = constrainPan(
              newTranslateX,
              newTranslateY,
              newScale
            );

            setZoomState({
              scale: newScale,
              translateX: constrained.translateX,
              translateY: constrained.translateY,
            });
          }
        } else {
          // Zoom out to 1x
          resetZoom();
        }
      }

      lastTapRef.current = now;
    },
    [zoomState.scale, constrainPan, resetZoom]
  );

  // Auto-scroll thumbnail container to center the active thumbnail (horizontal for mobile modal)
  const scrollThumbnailToCenter = useCallback(
    (index: number, containerRef: React.RefObject<HTMLDivElement | null>) => {
      const container = containerRef.current;
      if (!container) return;

      const thumbnails = container.querySelectorAll("button");
      const targetThumbnail = thumbnails[index];
      if (!targetThumbnail) return;

      // Horizontal scrolling for mobile modal bottom thumbnails
      const containerWidth = container.clientWidth;
      const thumbnailWidth = targetThumbnail.clientWidth;
      const thumbnailLeft = targetThumbnail.offsetLeft;

      // Calculate the scroll position to center the thumbnail horizontally
      const scrollLeft =
        thumbnailLeft - containerWidth / 2 + thumbnailWidth / 2;

      // Ensure scroll position is within bounds
      const maxScrollLeft = container.scrollWidth - containerWidth;
      const clampedScrollLeft = Math.max(
        0,
        Math.min(scrollLeft, maxScrollLeft)
      );

      container.scrollTo({
        left: clampedScrollLeft,
        behavior: "smooth",
      });
    },
    []
  );

  // Handle slide change from main carousel
  const handleSlideChange = useCallback(
    (index: number) => {
      onSlideChange(index);
    },
    [onSlideChange]
  );

  // Handle thumbnail click for main carousel
  const handleMainThumbnailClick = useCallback(
    (index: number) => {
      carouselApi?.scrollTo(index);
      setTimeout(() => {
        scrollThumbnailToCenter(index, mainThumbnailContainerRef);
      }, 50);
    },
    [carouselApi, scrollThumbnailToCenter]
  );

  // Handle modal thumbnail click
  const handleModalThumbnailClick = useCallback(
    (index: number) => {
      setModalCurrentSlide(index);
      setTimeout(() => {
        scrollThumbnailToCenter(index, modalThumbnailContainerRef);
      }, 50);
    },
    [scrollThumbnailToCenter]
  );

  // Handle image click to open modal
  const handleImageClick = useCallback(() => {
    setModalCurrentSlide(currentSlide);
    setModalOpen(true);
  }, [currentSlide]);

  // Modal navigation functions
  const goToPreviousImage = useCallback(() => {
    const newIndex =
      modalCurrentSlide > 0 ? modalCurrentSlide - 1 : images.length - 1;
    setModalCurrentSlide(newIndex);
    setTimeout(() => {
      scrollThumbnailToCenter(newIndex, modalThumbnailContainerRef);
    }, 50);
  }, [modalCurrentSlide, images.length, scrollThumbnailToCenter]);

  const goToNextImage = useCallback(() => {
    const newIndex =
      modalCurrentSlide < images.length - 1 ? modalCurrentSlide + 1 : 0;
    setModalCurrentSlide(newIndex);
    setTimeout(() => {
      scrollThumbnailToCenter(newIndex, modalThumbnailContainerRef);
    }, 50);
  }, [modalCurrentSlide, images.length, scrollThumbnailToCenter]);

  // Enhanced touch event handlers for both swipe navigation and pinch-to-zoom
  const handleTouchStart = useCallback(
    (e: React.TouchEvent) => {
      const touches = e.touches;

      if (touches.length === 1) {
        // Single touch - potential swipe or pan
        const touch = touches[0];

        if (zoomState.scale > 1) {
          // If zoomed in, start panning
          panStartRef.current = {
            x: touch.clientX,
            y: touch.clientY,
            translateX: zoomState.translateX,
            translateY: zoomState.translateY,
          };
        } else {
          // If not zoomed, prepare for swipe
          touchStartRef.current = {
            x: touch.clientX,
            y: touch.clientY,
            time: Date.now(),
          };
        }
        isPinching.current = false;
      } else if (touches.length === 2) {
        // Two touches - start pinch-to-zoom
        isPinching.current = true;
        touchStartRef.current = null;
        panStartRef.current = null;

        const distance = getTouchDistance(touches);
        const center = getTouchCenter(touches);

        lastTouchDistance.current = distance;
        lastTouchCenter.current = center;
      }
    },
    [
      zoomState.scale,
      zoomState.translateX,
      zoomState.translateY,
      getTouchDistance,
      getTouchCenter,
    ]
  );

  const handleTouchMove = useCallback(
    (e: React.TouchEvent) => {
      const touches = e.touches;

      if (touches.length === 2 && isPinching.current) {
        // Handle pinch-to-zoom
        e.preventDefault();

        const distance = getTouchDistance(touches);
        const center = getTouchCenter(touches);

        if (lastTouchDistance.current > 0) {
          const scaleChange = distance / lastTouchDistance.current;
          const newScale = Math.max(
            1,
            Math.min(4, zoomState.scale * scaleChange)
          );

          // Calculate translation to keep zoom centered on pinch point
          const containerRect = modalImageRef.current?.getBoundingClientRect();
          if (containerRect) {
            const centerX =
              center.x - containerRect.left - containerRect.width / 2;
            const centerY =
              center.y - containerRect.top - containerRect.height / 2;

            const scaleDiff = newScale - zoomState.scale;
            const newTranslateX =
              zoomState.translateX - (centerX * scaleDiff) / newScale;
            const newTranslateY =
              zoomState.translateY - (centerY * scaleDiff) / newScale;

            const constrained = constrainPan(
              newTranslateX,
              newTranslateY,
              newScale
            );

            setZoomState({
              scale: newScale,
              translateX: constrained.translateX,
              translateY: constrained.translateY,
            });
          }
        }

        lastTouchDistance.current = distance;
        lastTouchCenter.current = center;
      } else if (touches.length === 1 && !isPinching.current) {
        const touch = touches[0];

        if (panStartRef.current && zoomState.scale > 1) {
          // Handle panning when zoomed in
          e.preventDefault();

          const deltaX = touch.clientX - panStartRef.current.x;
          const deltaY = touch.clientY - panStartRef.current.y;

          const newTranslateX = panStartRef.current.translateX + deltaX;
          const newTranslateY = panStartRef.current.translateY + deltaY;

          const constrained = constrainPan(
            newTranslateX,
            newTranslateY,
            zoomState.scale
          );

          setZoomState((prev) => ({
            ...prev,
            translateX: constrained.translateX,
            translateY: constrained.translateY,
          }));
        } else if (touchStartRef.current && zoomState.scale === 1) {
          // Handle swipe detection when not zoomed
          const deltaX = Math.abs(touch.clientX - touchStartRef.current.x);
          const deltaY = Math.abs(touch.clientY - touchStartRef.current.y);

          // If horizontal movement is greater than vertical, prevent default scrolling
          if (deltaX > deltaY && deltaX > 10) {
            e.preventDefault();
          }
        }
      }
    },
    [zoomState, getTouchDistance, getTouchCenter, constrainPan]
  );

  const handleTouchEnd = useCallback(
    (e: React.TouchEvent) => {
      const changedTouches = e.changedTouches;

      if (isPinching.current) {
        // End of pinch gesture
        isPinching.current = false;
        lastTouchDistance.current = 0;
      } else if (changedTouches.length === 1) {
        if (touchStartRef.current && zoomState.scale === 1) {
          // Handle swipe navigation (only when not zoomed)
          const touch = changedTouches[0];
          const deltaX = touch.clientX - touchStartRef.current.x;
          const deltaY = touch.clientY - touchStartRef.current.y;
          const deltaTime = Date.now() - touchStartRef.current.time;

          // Check if it's a valid swipe
          const isHorizontalSwipe =
            Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 30;
          const isQuickSwipe = deltaTime < 800;
          const isLongEnoughSwipe = Math.abs(deltaX) > 80;

          if (isHorizontalSwipe && isQuickSwipe && isLongEnoughSwipe) {
            e.preventDefault();

            if (deltaX > 0) {
              goToPreviousImage();
            } else {
              goToNextImage();
            }
          } else if (
            !isHorizontalSwipe &&
            !isLongEnoughSwipe &&
            deltaTime < 300
          ) {
            // Check for double-tap (short tap without significant movement)
            handleDoubleTap(e);
          }
        } else if (zoomState.scale > 1 && !panStartRef.current) {
          // Handle double-tap when zoomed (for zoom out)
          const deltaTime = touchStartRef.current
            ? Date.now() - touchStartRef.current.time
            : 0;

          if (deltaTime < 300) {
            handleDoubleTap(e);
          }
        }
      }

      // Reset touch references
      touchStartRef.current = null;
      panStartRef.current = null;
    },
    [zoomState.scale, goToPreviousImage, goToNextImage, handleDoubleTap]
  );

  // Auto-scroll main thumbnail when currentSlide changes
  useEffect(() => {
    scrollThumbnailToCenter(currentSlide, mainThumbnailContainerRef);
  }, [currentSlide, scrollThumbnailToCenter]);

  // Auto-scroll modal thumbnail when modalCurrentSlide changes
  useEffect(() => {
    if (modalOpen) {
      const timeoutId = setTimeout(() => {
        scrollThumbnailToCenter(modalCurrentSlide, modalThumbnailContainerRef);
      }, 150);
      return () => clearTimeout(timeoutId);
    }
  }, [modalCurrentSlide, modalOpen, scrollThumbnailToCenter]);

  // Reset zoom when modal slide changes
  useEffect(() => {
    if (modalOpen) {
      resetZoom();
    }
  }, [modalCurrentSlide, modalOpen, resetZoom]);

  // Reset zoom when modal opens
  useEffect(() => {
    if (modalOpen) {
      resetZoom();
    }
  }, [modalOpen, resetZoom]);

  const { shareCurrentPage } = useShare();

  const handleShare = async (e: React.MouseEvent<HTMLButtonElement>) => {
    // Prevent any event bubbling that might interfere with touch events
    e.preventDefault();
    e.stopPropagation();

    try {
      const success = await shareCurrentPage();

      // Optional: Add haptic feedback on mobile devices if available
      if ("vibrate" in navigator && success) {
        navigator.vibrate(50);
      }
    } catch (error) {
      console.error("Share failed:", error);
    }
  };

  return (
    <>
      {/* Mobile Carousel - Only visible on mobile */}
      <div className="lg:hidden mb-6">
        <div className="w-full relative">
          {/* Share button - always visible with multiple fallback strategies */}
          <div className="absolute right-[-6px] top-[-22px] z-10">
            <button
              onClick={handleShare}
              className="cursor-pointer hover:opacity-80 active:scale-95 transition-all duration-200 flex items-center justify-center"
              style={
                {
                  "--tw-ring-color": primaryColor || "#036A38",
                  minWidth: "44px",
                  minHeight: "44px",
                  touchAction: "manipulation", // Prevent zoom on double-tap
                } as React.CSSProperties
              }
              aria-label="Share this product"
              title="Share this product"
              type="button"
            >
              <ShareIcon color={primaryColor} />
            </button>
          </div>
          <Carousel
            className="w-full relative"
            opts={{ loop: true }}
            setApi={setCarouselApi}
            onSlideChange={handleSlideChange}
          >
            <div className="rounded-lg overflow-hidden">
              <CarouselContent>
                {images.map((image, index) => (
                  <CarouselItem key={index}>
                    <div
                      className="relative aspect-square w-full flex items-center justify-center cursor-zoom-in bg-gray-50"
                      onClick={handleImageClick}
                    >
                      <Img
                        src={image}
                        alt={`${title || "Product"} - Image ${index + 1}`}
                        fill
                        className="object-contain h-full w-full"
                      />
                    </div>
                  </CarouselItem>
                ))}
              </CarouselContent>
            </div>

            {/* Dot indicators */}
            <div
              className="flex justify-center gap-2 mt-3"
              style={
                {
                  "--active-dot-color": primaryColor,
                  "--inactive-dot-color": `${primaryColor}4D`, // 30% opacity in hex
                } as React.CSSProperties
              }
            >
              <CarouselDots
                className="gap-2 mobile-product-carousel-dots"
                baseClassName="transition-all duration-300 w-2 h-2 rounded-full"
                activeClassName="bg-[var(--active-dot-color)]"
                inactiveClassName="bg-[var(--inactive-dot-color)]"
              />
            </div>
          </Carousel>

          {/* Thumbnail navigation */}
          <div className="mt-4 flex justify-center">
            <div
              ref={mainThumbnailContainerRef}
              className="max-w-full overflow-x-auto scrollbar-hide"
            >
              <div className="flex gap-2 min-w-fit px-4">
                {images.map((image, index) => (
                  <button
                    key={index}
                    onClick={() => handleMainThumbnailClick(index)}
                    className={`relative w-18 h-18 flex-shrink-0  overflow-hidden border transition-all duration-300 hover:opacity-80 ${
                      currentSlide === index
                        ? "ring-0"
                        : "border-gray-200 hover:border-gray-300 opacity-40"
                    }`}
                    style={
                      currentSlide === index
                        ? {
                            borderColor: primaryColor,
                            boxShadow: `0 0 0 2px ${primaryColor}30`,
                          }
                        : undefined
                    }
                  >
                    <Img
                      src={image}
                      alt={`${title || "Product"} - Thumbnail ${index + 1}`}
                      fill
                      className="object-cover"
                    />
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile-Specific Lightbox Modal */}
      {modalOpen && (
        <div
          className="fixed inset-0 z-[9999] bg-black"
          style={{
            touchAction: "pan-y", // Prevent modal zoom, allow vertical scrolling only
          }}
        >
          {/* Close button */}
          <button
            onClick={() => setModalOpen(false)}
            className="absolute right-4 top-4 z-[60] rounded-full bg-black/60 hover:bg-black/80 p-3 shadow-lg transition-all duration-200"
            style={{
              minWidth: "44px",
              minHeight: "44px",
              touchAction: "manipulation", // Prevent zoom on button
            }}
          >
            <X className="h-5 w-5 text-white" />
            <span className="sr-only">Close</span>
          </button>

          {/* Main modal content */}
          <div
            className="flex flex-col h-full"
            style={{
              touchAction: "pan-y", // Prevent modal zoom
            }}
          >
            {/* Image display area - takes most of the screen */}
            <div
              className="flex-1 relative min-h-0"
              onTouchStart={handleTouchStart}
              onTouchMove={handleTouchMove}
              onTouchEnd={handleTouchEnd}
              style={{
                touchAction: "pan-y", // Allow swipe detection but prevent modal zoom
              }}
            >
              <div className="absolute inset-0 flex items-center justify-center p-4">
                {/* Image container with proper centering and ISOLATED pinch-zoom */}
                <div
                  ref={modalImageRef}
                  className="relative w-full h-full flex items-center justify-center overflow-hidden"
                  style={{
                    touchAction: "none", // Disable all touch actions on container
                    WebkitUserSelect: "none",
                    userSelect: "none",
                    isolation: "isolate", // Create new stacking context
                  }}
                >
                  <div
                    className="relative w-full h-full"
                    style={{
                      touchAction: "none", // Handle touch events manually
                      overflow: "hidden",
                      transform: `translate(${zoomState.translateX}px, ${zoomState.translateY}px) scale(${zoomState.scale})`,
                      transformOrigin: "center center",
                      transition:
                        isPinching.current || panStartRef.current
                          ? "none"
                          : "transform 0.2s ease-out",
                      willChange: "transform", // Optimize for transforms
                    }}
                  >
                    <Img
                      src={images[modalCurrentSlide]}
                      alt={`${title || "Product"} - Modal Image ${
                        modalCurrentSlide + 1
                      }`}
                      fill
                      className="object-contain"
                      priority
                      sizes="100vw"
                      style={{
                        width: "100%",
                        height: "100%",
                        pointerEvents: "none", // Prevent image drag
                      }}
                    />
                  </div>
                </div>
              </div>

              {/* Navigation arrows - positioned on sides */}
              {images.length > 1 && (
                <>
                  <button
                    onClick={goToPreviousImage}
                    className="absolute left-3 top-1/2 -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full flex items-center justify-center transition-all duration-200 hover:scale-105 z-50 shadow-md"
                    style={{
                      minWidth: "44px",
                      minHeight: "44px",
                      width: "44px",
                      height: "44px",
                      touchAction: "manipulation", // Prevent zoom on button
                    }}
                  >
                    <ChevronLeft className="h-5 w-5" />
                  </button>
                  <button
                    onClick={goToNextImage}
                    className="absolute right-3 top-1/2 -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full flex items-center justify-center transition-all duration-200 hover:scale-105 z-50 shadow-md"
                    style={{
                      minWidth: "44px",
                      minHeight: "44px",
                      width: "44px",
                      height: "44px",
                      touchAction: "manipulation", // Prevent zoom on button
                    }}
                  >
                    <ChevronRight className="h-5 w-5" />
                  </button>
                </>
              )}

              {/* Slide indicator */}
              {images.length > 1 && (
                <div
                  className="absolute top-16 left-1/2 -translate-x-1/2 bg-black/70 text-white px-4 py-2 rounded-full text-sm z-40 font-medium"
                  style={{
                    touchAction: "none", // Prevent zoom on indicator
                  }}
                >
                  {modalCurrentSlide + 1} / {images.length}
                </div>
              )}
            </div>

            {/* Bottom thumbnail navigation */}
            {images.length > 1 && (
              <div
                className="bg-black/90 border-t border-white/10 safe-area-inset-bottom"
                style={{
                  touchAction: "pan-x", // Allow horizontal scrolling, prevent zoom
                }}
              >
                <div className="p-4">
                  <div
                    ref={modalThumbnailContainerRef}
                    className="overflow-x-auto scrollbar-hide"
                    style={{
                      scrollBehavior: "smooth",
                      WebkitOverflowScrolling: "touch",
                      touchAction: "pan-x", // Allow horizontal scrolling only
                    }}
                  >
                    <div className="flex gap-3 min-w-fit">
                      {images.map((image, index) => (
                        <button
                          key={index}
                          onClick={() => handleModalThumbnailClick(index)}
                          className={`relative flex-shrink-0 rounded-lg overflow-hidden border-2 transition-all duration-300 hover:opacity-80 ${
                            modalCurrentSlide === index
                              ? "border-white ring-2 ring-white/50 opacity-100"
                              : "border-white/30 hover:border-white/60 opacity-70"
                          }`}
                          style={{
                            minWidth: "56px",
                            minHeight: "56px",
                            width: "56px",
                            height: "56px",
                            touchAction: "manipulation", // Prevent zoom on thumbnails
                          }}
                        >
                          <Img
                            src={image}
                            alt={`${title || "Product"} - Modal Thumbnail ${
                              index + 1
                            }`}
                            fill
                            className="object-cover"
                          />
                        </button>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </>
  );
}
