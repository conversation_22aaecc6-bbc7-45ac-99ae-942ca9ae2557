"use client";

import React from "react";
import {
  Accordion,
  AccordionItem,
  AccordionContent,
} from "@/components/ui/accordion";
import { CustomAccordionTrigger } from "@/components/Common/FAQAccordion/CustomAccordionTrigger";
import { cn } from "@/libs/utils";
import { NutritionalFactsType } from "@/types/PDP/NutritionalFacts";
/**
 * NutritionalFactsAccordion Component
 *
 * Displays nutritional facts in an expandable accordion format.
 * Uses props for theming instead of Redux.
 * Supports both VARIANT and BYOB product types.
 *
 * @param data - Nutritional facts data
 * @param borderColor - Optional border color override
 * @param iconColor - Optional icon color override
 * @param className - Additional CSS classes
 * @param productType - Product type for conditional rendering
 * @param bundleVariants - Bundle variants for BYOB products
 */
export interface NutritionalFactsAccordionProps {
  data?: NutritionalFactsType;
  borderColor?: string;
  iconColor?: string;
  className?: string;
  productType?: "BYOB" | "VARIANT" | "COMBO";
  bundleVariants?: any[];
}

export function NutritionalFactsAccordion({
  data,
  borderColor = "#036A38", // Default theme color
  className,
  productType = "VARIANT",
  bundleVariants = [],
}: NutritionalFactsAccordionProps) {
  // Use provided colors or fall back to default theme color
  const finalBorderColor = borderColor;
  const chevronColor = "black";

  // Check if product type is BYOB or COMBO (both use the same logic)
  const isBundleProduct = productType === "BYOB" || productType === "COMBO";

  if (isBundleProduct) {
    // For BYOB and COMBO products, render each bundle variant's nutritional facts
    const validBundleVariants = bundleVariants.filter(
      (variant) =>
        variant.nutritional_facts && variant.nutritional_facts.show_component
    );

    if (validBundleVariants.length === 0) {
      console.log("❌ No valid bundle variants found - returning null");
      return null;
    }

    // Create a single accordion with all bundle variants as accordion items
    // This matches the FAQ accordion pattern for seamless borders
    return (
      <>
        <h3 className="font-narrow text-xl md:text-[28px] font-semibold leading-9 text-black">
          Nutritional Facts
        </h3>
        <Accordion
          type="single"
          collapsible
          className={cn(`border rounded-md overflow-hidden mt-4`, className)}
          style={{ borderColor: finalBorderColor }}
        >
          {validBundleVariants.map((variant, variantIndex) => {
            // Each bundle variant becomes a single accordion item
            // We'll use the first nutritional_fact_details item for the content
            const detail =
              variant.nutritional_facts.nutritional_fact_details[0];

            return (
              <AccordionItem
                key={variantIndex}
                value={`nutritional-facts-${variantIndex}`}
                className="bg-transparent border-0 border-b last:border-b-0"
                style={{ borderBottomColor: finalBorderColor }}
              >
                <CustomAccordionTrigger
                  className="px-6 py-4 text-left text-lg lg: lg:text-[22px] font-narrow font-semibold hover:no-underline cursor-pointer"
                  iconColor={chevronColor}
                >
                  {variant.title}
                </CustomAccordionTrigger>
                <AccordionContent className="px-4 font-normal text-base lg:text-lg font-obviously">
                  {/* Description */}
                  {variant.nutritional_facts.description && (
                    <p className="mb-4 text-lg leading-7 font-normal text-[#1a181e] font-obviously flex items-start">
                      {variant.nutritional_facts.description}
                    </p>
                  )}

                  {/* Main nutritional items - Using same styling as VARIANT */}
                  <ul className="list-none">
                    {detail?.nutritional_fact_items?.map(
                      (item: any, itemIndex: number) => (
                        <li key={itemIndex}>
                          {/* Main item */}
                          <div className="text-base leading-7 font-normal text-[#1a181e] font-obviously flex items-start">
                            <div className="flex-shrink-0">{item.key}</div>
                            <div className="flex-1 border-t-2 border-dotted border-[#00000080] mx-2 mt-[18px]"></div>
                            <div className="flex-shrink-0">{item.value}</div>
                          </div>

                          {/* Sub items */}
                          {item.nutritional_fact_sub_items &&
                            item.nutritional_fact_sub_items.length > 0 && (
                              <ul className="list-none ml-4 mt-1 space-y-1">
                                {item.nutritional_fact_sub_items.map(
                                  (subItem: any, subIndex: number) => (
                                    <li
                                      key={subIndex}
                                      className="leading-6 text-base font-normal text-[#1a181e] font-obviously flex items-start"
                                    >
                                      <div className="flex-shrink-0">
                                        {subItem.key}
                                      </div>
                                      <div className="flex-1 border-t-2 border-dotted border-[#00000080] mx-2 mt-[18px]"></div>
                                      <div className="flex-shrink-0">
                                        {subItem.value}
                                      </div>
                                    </li>
                                  )
                                )}
                              </ul>
                            )}
                        </li>
                      )
                    )}
                  </ul>
                </AccordionContent>
              </AccordionItem>
            );
          })}
        </Accordion>
      </>
    );
  }

  // For VARIANT products, use regular data
  // Early return if no data or component should not be shown
  if (!data || !data.show_component) {
    return null;
  }

  return (
    <Accordion
      type="single"
      collapsible
      className={cn(`border rounded-md overflow-hidden mt-6`, className)}
      style={{ borderColor: finalBorderColor }}
    >
      {data.nutritional_fact_details.map((detail, detailIndex) => (
        <AccordionItem
          key={detailIndex}
          value={`nutritional-facts-${detailIndex}`}
          className="bg-transparent border-0 border-b last:border-b-0"
          style={{ borderBottomColor: finalBorderColor }}
        >
          <CustomAccordionTrigger
            className="px-6 py-4 text-left text-lg lg: lg:text-[22px] font-narrow font-semibold hover:no-underline cursor-pointer"
            iconColor={chevronColor}
          >
            Nutritional Facts
          </CustomAccordionTrigger>
          <AccordionContent className="px-4 font-normal text-base lg:text-lg font-obviously">
            {/* Description */}
            {data.description && (
              <p className="mb-4 text-lg leading-7 font-normal text-[#1a181e] font-obviously flex items-start">
                {data.description}
              </p>
            )}

            {/* Main nutritional items */}
            <ul className="list-none">
              {detail.nutritional_fact_items.map((item, itemIndex) => (
                <li key={itemIndex}>
                  {/* Main item */}
                  <div className="text-base leading-7 font-normal text-[#1a181e] font-obviously flex items-start">
                    <div className="flex-shrink-0">{item.key}</div>
                    <div className="flex-1 border-t-2 border-dotted border-[#00000080] mx-2 mt-[18px]"></div>
                    <div className="flex-shrink-0">{item.value}</div>
                  </div>

                  {/* Sub items */}
                  {item.nutritional_fact_sub_items &&
                    item.nutritional_fact_sub_items.length > 0 && (
                      <ul className="list-none ml-4 mt-1 space-y-1">
                        {item.nutritional_fact_sub_items.map(
                          (subItem, subIndex) => (
                            <li
                              key={subIndex}
                              className="leading-6 text-base font-normal text-[#1a181e] font-obviously flex items-start"
                            >
                              <div className="flex-shrink-0">{subItem.key}</div>
                              <div className="flex-1 border-t-2 border-dotted border-[#00000080] mx-2 mt-[18px]"></div>
                              <div className="flex-shrink-0">
                                {subItem.value}
                              </div>
                            </li>
                          )
                        )}
                      </ul>
                    )}
                </li>
              ))}
            </ul>
          </AccordionContent>
        </AccordionItem>
      ))}
    </Accordion>
  );
}

export default NutritionalFactsAccordion;
