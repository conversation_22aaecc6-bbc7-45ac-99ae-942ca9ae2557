"use client";
import React from "react";
import LoyaltyCashback from "@/assets/icons/LoyaltyCashback";
import InfoIcon from "@/assets/icons/Info";
import { MobileTooltip } from "@/components/ui/tooltip";
import { calculateDiscount } from "@/utils/pricing";
// Removed Redux theme hook - now using props

/**
 * PriceSection Component
 *
 * Displays product pricing information including current price, original price,
 * discount percentage, and loyalty cashback information.
 * Uses ProductThemeContext for consistent theming.
 *
 * @param currentPrice - Current selling price
 * @param originalPrice - Original price before discount
 * @param cashbackPercentage - Cashback percentage (default: 2)
 * @param loyaltyPoints - Loyalty points earned
 */
interface PriceSectionProps {
  currentPrice: number;
  originalPrice: number;
  cashbackPercentage?: number;
  loyaltyPoints: number;
  primaryColor?: string;
}

const PriceSection: React.FC<PriceSectionProps> = ({
  currentPrice,
  originalPrice,
  cashbackPercentage = 2,
  loyaltyPoints,
  primaryColor = "#036A38", // Default theme color
}) => {
  return (
    <div className="mb-4">
      <div className="mb-5">
        <div className="flex items-center gap-2">
          <p
            className="pb-2.5 font-[560] text-nowrap font-obviously text-[26px]"
            style={{ color: primaryColor }}
          >
            ₹{currentPrice?.toLocaleString()}
          </p>
          <p className="line-through text-[#1a181e] font-normal text-nowrap font-obviously text-base leading-6">
            ₹{originalPrice?.toLocaleString()}
          </p>
          <span
            className="flex items-center gap-1 text-[10.5px] font-semibold leading-[11px] font-obviously rounded-[2.6px] p-1"
            style={{
              color: primaryColor,
              backgroundColor: `${primaryColor}4d`,
            }}
          >
            ({calculateDiscount(currentPrice, originalPrice)}% OFF)
          </span>
        </div>
        <p className="text-[#464646] font-obviously font-normal text-sm">
          MRP inclusive of all taxes
        </p>
      </div>

      {/* Loyalty Cashback Section */}
      <div className="flex items-center gap-1.5">
        <LoyaltyCashback color={primaryColor} />
        <p
          className="font-obviously font-[550] text-sm pb-[2px]"
          style={{ color: primaryColor }}
        >
          Earn {cashbackPercentage}% cashback on this purchase
        </p>
        <MobileTooltip
          content={`Earn ${loyaltyPoints} Truth points on this order. 1 point = 1 rupee`}
          contentClassName="text-white text-[8px] font-[540] leading-3"
        >
          <InfoIcon color={primaryColor} />
        </MobileTooltip>
      </div>
    </div>
  );
};

export default PriceSection;
