import React from "react";
import LoyaltyCashback from "@/assets/icons/LoyaltyCashback";
import InfoIcon from "@/assets/icons/Info";
import { MobileTooltip } from "@/components/ui/tooltip";
import { ProductPricingProps } from "../../types";
import { useProductContext } from "../../context/ProductContext";
import { SameDayDelivery } from "@/assets/icons";
import { formatPrice } from "../../utils";

/**
 * ProductPricing Component
 *
 * Displays product pricing information including current price, original price,
 * discount percentage, and loyalty cashback information
 */
export const ProductPricing: React.FC<ProductPricingProps> = ({
  strapiProduct,
  medusaProduct, // eslint-disable-line @typescript-eslint/no-unused-vars
  productType,
  medusaBundleData, // eslint-disable-line @typescript-eslint/no-unused-vars
}) => {
  const primaryColor = strapiProduct?.primary_color || "#036A38";

  // Get pricing data from context (calculated based on active variant)
  const {
    loyaltyPoints,
    discountPercentage,
    formattedCurrentPrice,
    formattedOriginalPrice,
    hasDiscount,
  } = useProductContext();

  console.log("medusaBundleData@@: ", medusaBundleData);

  const startingPrice = medusaBundleData?.bundle?.starting_from_price;

  const cashbackPercentage = 2; // Default cashback percentage

  return (
    <>
      {strapiProduct?.show_delievery_option && (
        <SameDayDelivery color={primaryColor} />
      )}
      <div className="mb-4">
        <div className="mb-5">
          {productType === "VARIANT" && (
            <div className="flex items-center gap-2">
              <p
                className="pb-2.5 font-[560] text-nowrap font-obviously text-[26px]"
                style={{ color: primaryColor }}
              >
                {formattedCurrentPrice}
              </p>
              {hasDiscount && (
                <>
                  <p className="line-through text-[#1a181e] font-normal text-nowrap font-obviously text-base leading-6">
                    {formattedOriginalPrice}
                  </p>
                  <span
                    className="flex items-center gap-1 text-[10.5px] font-semibold leading-[11px] font-obviously rounded-[2.6px] p-1"
                    style={{
                      color: primaryColor,
                      backgroundColor: `${primaryColor}4d`,
                    }}
                  >
                    ({discountPercentage}% OFF)
                  </span>
                </>
              )}
            </div>
          )}
          {productType === "BYOB" && startingPrice && (
            <div style={{ color: primaryColor }}>
              <span className="font-narrow text-base font-semibold">
                {" "}
                Starting from
              </span>
              <span className="text-[26px] font-narrow font-semibold pb-1 pl-1">
                {formatPrice(startingPrice)}
              </span>
            </div>
          )}
          <p className="text-[#464646] font-obviously font-normal text-sm">
            MRP inclusive of all taxes
          </p>

          {productType === "BYOB" && (
            <p className="mt-3 text-xs text-[#464646] font-obviously font-normal">
              Total MRP and Net Quantity will vary based on your product
              selection.
            </p>
          )}
        </div>

        {/* Loyalty Cashback Section */}
        <div className="flex items-center gap-1.5">
          <LoyaltyCashback color={primaryColor} />
          <p
            className="font-obviously font-[550] text-sm pb-[2px]"
            style={{ color: primaryColor }}
          >
            Earn {cashbackPercentage}% cashback on this purchase
          </p>
          <MobileTooltip
            content={`Earn ${loyaltyPoints} Truth points on this order. 1 point = 1 rupee`}
            contentClassName="text-white text-[8px] font-[540] leading-3"
          >
            <InfoIcon color={primaryColor} />
          </MobileTooltip>
        </div>
      </div>
    </>
  );
};
