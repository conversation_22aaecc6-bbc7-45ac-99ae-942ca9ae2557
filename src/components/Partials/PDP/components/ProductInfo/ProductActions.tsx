import React, { useState, useCallback } from "react";
import VariantSelector from "../../VariantSelector";
import QuantitySelector from "../../QuantitySelector";
import AddToCartButton from "../../AddToCartButton";
import BestPriceCoupon from "../../BestPriceCoupon";
import DeliveryInfo from "../../DeliveryInfoCard";
import { ProductActionsProps } from "../../types";
import BundleVariantSelector from "../../BundleVariantSelector";

/**
 * ProductActions Component
 *
 * Contains all product interaction elements:
 * - Variant selection
 * - Quantity selection
 * - Add to cart button
 * - Best price coupon
 * - Delivery information
 */
export const ProductActions: React.FC<ProductActionsProps> = ({
  strapiProduct,
  medusaProduct,
  onAddToCart,
  onCouponClick,
  productType,
  bundleVariants,
  medusaBundleData,
}) => {
  // State for bundle variant quantities (only used for BYOB products)
  const [selectedQuantities, setSelectedQuantities] = useState<
    Record<string, number>
  >({});

  const handleCouponClick = () => {
    if (onCouponClick) {
      onCouponClick();
    } else {
      console.log("Coupon clicked");
    }
  };

  const handleQuantityChange = useCallback(
    (variantId: string, quantity: number) => {
      setSelectedQuantities((prev) => ({
        ...prev,
        [variantId]: quantity,
      }));
    },
    []
  );

  return (
    <>
      <div className="flex flex-col">
        {/* Variant Selector - Only for VARIANT products */}
        {productType !== "BYOB" && productType !== "COMBO" && (
          <VariantSelector
            strapiProduct={strapiProduct}
            medusaProduct={medusaProduct}
          />
        )}

        {/* Bundle Variant Selector - For BYOB and COMBO products */}
        {(productType === "BYOB" || productType === "COMBO") &&
          strapiProduct &&
          bundleVariants && (
            <BundleVariantSelector
              strapiProduct={strapiProduct}
              bundleVariants={bundleVariants}
              productType={productType}
              onQuantityChange={handleQuantityChange}
              selectedQuantities={selectedQuantities}
              medusaBundleData={medusaBundleData}
            />
          )}

        {/* Quantity Selector - Only for VARIANT products */}
        {/* {productType !== "BYOB" && ( */}
        <QuantitySelector
          strapiProduct={strapiProduct}
          medusaProduct={medusaProduct}
        />
        {/* )} */}

        {/* Add to Cart Button */}
        <AddToCartButton
          strapiProduct={strapiProduct}
          medusaProduct={medusaProduct}
          onAddToCart={onAddToCart}
        />
      </div>

      {/* Best Price Coupon */}
      <BestPriceCoupon
        productType={productType}
        strapiProduct={strapiProduct}
        medusaProduct={medusaProduct}
        onCouponClick={handleCouponClick}
      />

      {/* Delivery Information */}
      <DeliveryInfo
        strapiProduct={strapiProduct}
        medusaProduct={medusaProduct}
      />
    </>
  );
};
