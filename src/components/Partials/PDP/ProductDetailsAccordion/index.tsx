"use client";

import React from "react";
import {
  Accordion,
  AccordionItem,
  AccordionContent,
} from "@/components/ui/accordion";
import { CustomAccordionTrigger } from "@/components/Common/FAQAccordion/CustomAccordionTrigger";
import { cn } from "@/libs/utils";
import { ProductDetailsExtraType } from "@/types/PDP/ProductDetailsExtra";
/**
 * ProductDetailsAccordion Component
 *
 * Displays product details in an expandable accordion format.
 * Uses props for theming instead of Redux.
 *
 * @param data - Product details data
 * @param borderColor - Optional border color override
 * @param iconColor - Optional icon color override
 * @param className - Additional CSS classes
 */
export interface ProductDetailsAccordionProps {
  data?: ProductDetailsExtraType;
  borderColor?: string;
  iconColor?: string;
  className?: string;
}

export function ProductDetailsAccordion({
  data,
  borderColor = "#036A38", // Default theme color
  className,
}: ProductDetailsAccordionProps) {
  // Use provided colors or fall back to default theme color
  const finalBorderColor = borderColor;
  const chevronColor = "black";

  // Early return if no data or component should not be shown
  if (!data || !data.show_component) {
    return null;
  }

  return (
    <Accordion
      type="single"
      collapsible
      className={cn(`border rounded-md overflow-hidden mt-6`, className)}
      style={{ borderColor: finalBorderColor }}
    >
      <AccordionItem
        className="bg-transparent border-0 border-b last:border-b-0"
        value="product-details"
        style={{ borderBottomColor: finalBorderColor }}
      >
        <CustomAccordionTrigger
          className="px-6 py-4 text-left text-lg lg:text-[22px] font-narrow font-semibold hover:no-underline cursor-pointer"
          iconColor={chevronColor}
        >
          Product Details
        </CustomAccordionTrigger>
        <AccordionContent className="px-6 font-normal text-base lg:text-lg font-obviously">
          {/* Main nutritional items */}
          <ul className="list-none">
            {data.product_detail_extra_items?.map((detail, detailIndex) => {
              const hasNewline = detail.value?.includes("\n");

              return (
                <li key={detailIndex} className="mb-2">
                  {hasNewline ? (
                    // Render key on top, value below
                    <div className="text-base leading-7 font-normal text-[#1a181e] font-obviously">
                      <strong className="block">{detail.key}:</strong>
                      <span className="block ml-0 whitespace-pre-line">
                        {detail.value}
                      </span>
                    </div>
                  ) : (
                    // Render key and value inline
                    <div className="inline-block text-base leading-7 font-normal text-[#1a181e] font-obviously items-start">
                      <strong className="flex-shrink-0">{detail.key}:</strong>
                      <span className="ml-1.5">{detail.value}</span>
                    </div>
                  )}
                </li>
              );
            })}
          </ul>
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
}

export default ProductDetailsAccordion;
