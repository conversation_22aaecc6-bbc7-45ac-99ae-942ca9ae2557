import React from "react";
import { ProductDetailsType } from "@/types/Collections/ProductDetails";
import { UniversalCarousel } from "@/components/Common/CarouselWrapper.tsx";
import { FlipCard } from "@/components/Common/FlipCard.tsx";
import { getGlobalSettings } from "@/libs/middlewareAPIs";
const RealReviews = async ({
  productData,
}: {
  productData: ProductDetailsType;
}) => {
  const realReviewData = await getGlobalSettings();
  const reviews =
    realReviewData?.real_people_reviews?.review_items?.flippable_card_items ||
    [];
  const title =
    realReviewData?.real_people_reviews?.title || "Real People. Real Reviews.";
  const primaryColor = productData.primary_color;

  const isShow = productData.show_real_people_reviews;
  if (!isShow) return null;

  return (
    <section className="flex flex-col gap-4 max-w-[1020px] mx-auto">
      <h2 className="lg:text-[32px] text-[28px] font-semibold font-narrow text-black text-center">
        {title}
      </h2>
      <UniversalCarousel
        useNativeScrollbar={true}
        hideScrollbar={false}
        gapClassName="gap-4"
        scrollbarColor={primaryColor}
      >
        {reviews.map((card, i) => (
          <FlipCard
            key={i}
            frontImage={card.front?.web?.url || ""}
            backImage={card.back?.web?.url || ""}
            frontAlt={card.front?.web?.alternativeText || "key-feature-front"}
            backAlt={card.back?.web?.alternativeText || "key-feature-back"}
            width={200}
            height={368}
            className="w-[200px] h-[368px]"
          />
        ))}
      </UniversalCarousel>
    </section>
  );
};

export default RealReviews;
