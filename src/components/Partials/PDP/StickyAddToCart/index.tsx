// TODO: Add product image and Price Based On Conditon
"use client";

import React, { useState, useMemo } from "react";
import { cn } from "@/libs/utils";
import { StickyAddToCartProps } from "../types";
import { useProductContext } from "../context/ProductContext";
import { LoyaltyCashback } from "@/assets/icons/LoyaltyCashback";
import InfoIcon from "@/assets/icons/Info";
import { MobileTooltip } from "@/components/ui/tooltip";

/**
 * StickyAddToCart Component
 *
 * A sticky bottom component that shows Add to Cart functionality
 * with smooth slide animations and loyalty cashback information.
 *
 * Features:
 * - 92px total height with 25px loyalty section
 * - Smooth slide up/down transitions
 * - Hides when What's Inside section is visible
 * - Responsive design
 */
export const StickyAddToCart: React.FC<
  StickyAddToCartProps & { isVisible: boolean }
> = ({
  strapiProduct,
  medusaProduct, // eslint-disable-line @typescript-eslint/no-unused-vars
  onAddToCart,
  onCouponClick, // eslint-disable-line @typescript-eslint/no-unused-vars
  productType = "VARIANT", // eslint-disable-line @typescript-eslint/no-unused-vars
  bundleVariants = [], // eslint-disable-line @typescript-eslint/no-unused-vars
  isVisible,
}) => {
  const [isAddingToCart, setIsAddingToCart] = useState(false);
  const {
    activeVariant,
    quantity,
    currentPrice,
    medusaProduct: contextMedusaProduct,
  } = useProductContext();

  const primaryColor = strapiProduct?.primary_color || "#036A38";
  const backgroundColor = strapiProduct?.bg_color || "#ffffff";
  const cashbackPercentage = 2; // Default cashback percentage
  const loyaltyPoints = Math.floor(
    currentPrice * quantity * (cashbackPercentage / 100)
  );

  // Calculate button state based on active variant
  const buttonState = useMemo(() => {
    if (isAddingToCart) {
      return {
        text: "Adding...",
        disabled: true,
      };
    }

    if (!activeVariant) {
      return {
        text: "Select Variant",
        disabled: true,
      };
    }

    return {
      text: "Add to Cart",
      disabled: false,
    };
  }, [activeVariant, isAddingToCart]);

  const handleAddToCart = async () => {
    if (buttonState.disabled || !activeVariant) return;

    setIsAddingToCart(true);

    try {
      // Call external callback if provided
      if (onAddToCart && contextMedusaProduct) {
        onAddToCart({
          product: contextMedusaProduct,
          variant: activeVariant,
          quantity: quantity,
          totalPrice: currentPrice * quantity,
        });
      }
    } catch (error) {
      console.error("Error adding to cart:", error);
    } finally {
      setIsAddingToCart(false);
    }
  };

  // Don't render if no product data
  if (!contextMedusaProduct || !strapiProduct) {
    return null;
  }

  return (
    <div
      className={cn(
        "fixed bottom-0 left-0 right-0 z-40   shadow-lg transition-transform duration-300 ease-in-out",
        isVisible ? "translate-y-0" : "translate-y-full"
      )}
      style={{ backgroundColor: backgroundColor }}
    >
      {/* Loyalty Section - 25px height */}
      <div
        className="flex items-center justify-center px-4 border-b border-gray-100"
        style={{ height: "25px" }}
      >
        <div className="flex items-center gap-1.5">
          <LoyaltyCashback color={primaryColor} />
          <p
            className="font-obviously font-[550] text-xs"
            style={{ color: primaryColor }}
          >
            Earn {cashbackPercentage}% cashback on this purchase
          </p>
          <MobileTooltip
            content={`Earn ${loyaltyPoints} Truth points on this order. 1 point = 1 rupee`}
            contentClassName="text-white text-[8px] font-[540] leading-3"
          >
            <InfoIcon color={primaryColor} />
          </MobileTooltip>
        </div>
      </div>

      {/* Add to Cart Section - 92px height */}
      <div
        className="flex items-center justify-center h-[48px] md:h-[92px]"
        style={{ backgroundColor: "rgba(255,255,255,0.6)" }}
      >
        {/* Product Image - Optional - Hide On mobile  */}
        {/* <Image
          src={"/images/products/badaam/highlight/1.webp"}
          alt={contextMedusaProduct?.title}
          width={78}
          height={78}
          className="rounded-sm object-cover"
          style={{ border: `1px solid ${primaryColor}` }}
        /> */}

        {/* Product Info - Hide On Mobile*/}
        <div className="hidden md:block max-w-[320px] mr-[98px] ml-4.5 text-2xl leading-8 mb-0">
          <h3
            className="font-narrow font-semibold text-2xl leading-8 text-gray-900"
            style={{ color: primaryColor }}
          >
            {contextMedusaProduct?.title}
          </h3>
        </div>

        {/* Product Selling Price - Optional - Hide On Mobile */}
        {/* <p
          className="hidden md:block text-xl font-semibold font-obviously mr-2"
          style={{ color: primaryColor }}
        >
          ₹990
        </p> */}

        {/* Add to Cart Button */}
        <button
          className="flex-1 md:flex-none uppercase cursor-pointer h-12.5 p-2 rounded-none md:rounded-sm text-center font-semibold text-sm font-obviously md:border min-w-[192px] transition-opacity duration-200 md:mr-2.5"
          style={{
            color: primaryColor,
            backgroundColor: buttonState.disabled ? "#cccccc" : "white",
            borderColor: buttonState.disabled ? "#cccccc" : primaryColor,
            opacity: buttonState.disabled ? 0.6 : 1,
          }}
          onClick={handleAddToCart}
          disabled={buttonState.disabled}
        >
          {buttonState.text}
        </button>

        {/* Buy Now Button */}
        <button
          className="flex-1 md:flex-none uppercase cursor-pointer h-12.5 p-2 rounded-none md:rounded-sm text-white text-center font-semibold text-sm font-obviously md:border min-w-[192px] transition-opacity duration-200"
          style={{
            backgroundColor: buttonState.disabled ? "#cccccc" : primaryColor,
            borderColor: buttonState.disabled ? "#cccccc" : primaryColor,
            opacity: buttonState.disabled ? 0.6 : 1,
          }}
          onClick={handleAddToCart}
          disabled={buttonState.disabled}
        >
          BUY NOW
        </button>
      </div>
    </div>
  );
};
