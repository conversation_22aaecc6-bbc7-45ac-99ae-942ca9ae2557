"use client";

import { ProductDetailsType } from "@/types/Collections/ProductDetails";
import React from "react";

const ProductAdditionalDescription = (props: {
  productData: ProductDetailsType;
}) => {
  // Early return if no data or description provided
  if (!props.productData.additional_description?.show_component) return null;

  return (
    <div
      className="mt-12.5 font-obviously [&>ul]:list-disc pl-4"
      dangerouslySetInnerHTML={{
        __html: props.productData.additional_description?.description,
      }}
    />
  );
};

export default ProductAdditionalDescription;
