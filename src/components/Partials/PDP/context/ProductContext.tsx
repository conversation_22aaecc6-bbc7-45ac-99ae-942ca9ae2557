"use client";

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useMemo,
} from "react";
import { ProductDetailsType } from "@/types/Collections/ProductDetails";
import {
  ExtendedMedusaProductWithStrapiProduct,
  ExtendedVariant,
} from "@/types/Medusa/Product";

// ============================================================================
// CONTEXT TYPES
// ============================================================================

interface ProductContextState {
  // Product data
  strapiProduct: ProductDetailsType | null;
  medusaProduct: ExtendedMedusaProductWithStrapiProduct | null;

  // UI state
  activeVariant: ExtendedVariant | null;
  quantity: number;

  // Computed values
  combinedImages: string[];
  currentPrice: number;
  originalPrice: number;
  loyaltyPoints: number;
  discountPercentage: number;
  hasDiscount: boolean;
  formattedCurrentPrice: string;
  formattedOriginalPrice: string;
}

interface ProductContextActions {
  setActiveVariant: (variant: ExtendedVariant) => void;
  setQuantity: (quantity: number) => void;
  incrementQuantity: () => void;
  decrementQuantity: () => void;
}

interface ProductContextValue
  extends ProductContextState,
    ProductContextActions {}

interface ProductProviderProps {
  children: React.ReactNode;
  strapiProduct: ProductDetailsType | null;
  medusaProduct: ExtendedMedusaProductWithStrapiProduct | null;
}

// ============================================================================
// CONTEXT CREATION
// ============================================================================

const ProductContext = createContext<ProductContextValue | undefined>(
  undefined
);

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Generate combined images from global and variant-specific sources
 */
const generateCombinedImages = (
  medusaProduct: ExtendedMedusaProductWithStrapiProduct | null,
  activeVariant: ExtendedVariant | null
): string[] => {
  const globalImages: string[] = [];
  const variantImages: string[] = [];

  // Extract global product images
  if (medusaProduct?.images && Array.isArray(medusaProduct.images)) {
    globalImages.push(
      ...medusaProduct.images.map((img: any) => img.url || img)
    );
  }

  // Extract variant-specific images
  if (
    activeVariant?.variant_image &&
    Array.isArray(activeVariant.variant_image)
  ) {
    const sortedVariantImages = [...activeVariant.variant_image].sort(
      (a, b) => a.rank - b.rank
    );
    variantImages.push(...sortedVariantImages.map((img) => img.url));
  }

  // Combine global images first, then variant-specific images
  const combined = [...globalImages, ...variantImages];

  // Fallback to sample images if no images are available
  if (combined.length === 0) {
    return [
      "/images/products/pdp/image.png",
      "/images/products/badaam/highlight/2.webp",
      "/images/products/badaam/highlight/3.webp",
      "/images/products/badaam/highlight/4.webp",
      "/images/products/badaam/highlight/5.webp",
    ];
  }

  return combined;
};

/**
 * Calculate pricing information from active variant
 */
const calculatePricing = (activeVariant: ExtendedVariant | null) => {
  if (!activeVariant) {
    return {
      currentPrice: 0,
      originalPrice: 0,
      loyaltyPoints: 0,
      discountPercentage: 0,
      hasDiscount: false,
      formattedCurrentPrice: "₹0",
      formattedOriginalPrice: "₹0",
    };
  }

  const currentPrice = activeVariant.calculated_price?.calculated_amount || 0;

  const originalPrice =
    activeVariant.extended_product_variants?.compare_at_price ||
    activeVariant.calculated_price?.calculated_amount ||
    0;

  const loyaltyPoints = Math.floor(currentPrice * 0.02);

  // Calculate discount percentage
  const discountPercentage =
    originalPrice > currentPrice && originalPrice > 0
      ? Math.round(((originalPrice - currentPrice) / originalPrice) * 100)
      : 0;

  return {
    currentPrice,
    originalPrice,
    loyaltyPoints,
    discountPercentage,
    hasDiscount: originalPrice > currentPrice && discountPercentage > 0,
    formattedCurrentPrice: `₹${currentPrice.toLocaleString()}`,
    formattedOriginalPrice: `₹${originalPrice.toLocaleString()}`,
  };
};

// ============================================================================
// PROVIDER COMPONENT
// ============================================================================

export const ProductProvider: React.FC<ProductProviderProps> = ({
  children,
  strapiProduct,
  medusaProduct,
}) => {
  const [activeVariant, setActiveVariant] = useState<ExtendedVariant | null>(
    null
  );
  const [quantity, setQuantity] = useState(1);

  // Initialize active variant when medusaProduct changes
  useEffect(() => {
    if (medusaProduct?.variants && medusaProduct.variants.length > 0) {
      setActiveVariant(medusaProduct.variants[0]);
    }
  }, [medusaProduct]);

  // Calculate computed values
  const computedValues = useMemo(() => {
    const combinedImages = generateCombinedImages(medusaProduct, activeVariant);
    const pricing = calculatePricing(activeVariant);

    return {
      combinedImages,
      ...pricing,
    };
  }, [medusaProduct, activeVariant]);

  // Actions
  const incrementQuantity = () => setQuantity((prev) => prev + 1);
  const decrementQuantity = () => setQuantity((prev) => Math.max(1, prev - 1));

  const contextValue: ProductContextValue = {
    // Product data
    strapiProduct,
    medusaProduct,

    // UI state
    activeVariant,
    quantity,

    // Computed values
    ...computedValues,

    // Actions
    setActiveVariant,
    setQuantity,
    incrementQuantity,
    decrementQuantity,
  };

  return (
    <ProductContext.Provider value={contextValue}>
      {children}
    </ProductContext.Provider>
  );
};

// ============================================================================
// HOOK
// ============================================================================

export const useProductContext = (): ProductContextValue => {
  const context = useContext(ProductContext);
  if (context === undefined) {
    throw new Error("useProductContext must be used within a ProductProvider");
  }
  return context;
};
