import { ProductDetailsType } from "@/types/Collections/ProductDetails";
import { NavigationSection } from "./types";

/**
 * Checks for direct component rendering (components not in template.blocks)
 * These are components that are rendered directly in the component tree
 *
 * @param strapiProduct - Strapi product data
 * @returns Object with boolean flags for each direct component
 */
const checkDirectComponentRendering = (strapiProduct?: ProductDetailsType) => {
  const checks = {
    bestPriceCoupon: false,
    offers: false,
    ingredients: false,
  };

  if (!strapiProduct) {
    return checks;
  }

  // Check for BestPriceCoupon rendering conditions
  // BestPriceCoupon is rendered in ProductActions.tsx and appears to always render
  // when the component is included (no conditional rendering in the component itself)
  // It calculates price from medusaProduct.variants[0], so it needs variants to be meaningful

  // Since BestPriceCoupon is imported and rendered directly in ProductActions.tsx,
  // we can assume it will be rendered for most products. The key indicators are:
  // 1. Product exists (strapiProduct is truthy)
  // 2. This is likely a regular product page (not a bundle-only page)

  // For now, we'll assume BestPriceCoupon is rendered for all products
  // unless there are specific conditions that would prevent it
  const isRegularProduct = !!strapiProduct;

  const productId = (strapiProduct as any)?.id;
  const productSlug = (strapiProduct as any)?.slug;
  const hasProductData = !!productId || !!productSlug;

  // BestPriceCoupon is rendered directly in ProductActions.tsx for all products
  checks.bestPriceCoupon = isRegularProduct && hasProductData;
  checks.offers = checks.bestPriceCoupon; // Offers section includes BestPriceCoupon

  // WhatsInsideSection (ingredients) is rendered directly in ProductContent.tsx for all products
  // It shows ingredient breakdown and nutritional information
  checks.ingredients = isRegularProduct && hasProductData;

  // TEMPORARY DEBUG: Force offers to true for debugging
  if (strapiProduct) {
    checks.offers = true;
    checks.bestPriceCoupon = true;
    checks.ingredients = true;
  }

  return checks;
};

/**
 * Detects available sections based on specific block types in product data
 *
 * @param strapiProduct - Strapi product data
 * @returns Array of available navigation sections
 */
export const detectAvailableSections = (
  strapiProduct?: ProductDetailsType
): NavigationSection[] => {
  const availableSections: NavigationSection[] = [];

  // Access blocks from template.blocks path
  const productBlocks = (strapiProduct as any)?.template?.blocks;

  if (!productBlocks || !Array.isArray(productBlocks)) {
    return availableSections;
  }

  // Check for direct component rendering (components not in template.blocks)

  const directComponentChecks = checkDirectComponentRendering(strapiProduct);

  // Define specific sections to look for based on __typename values in template.blocks
  const sectionConfigs = [
    {
      id: "reviews-section",
      label: "Reviews",
      blockTypes: ["ComponentReviewTemplatesVerifiedReviewsTemplate"],
      order: 1,
    },
    {
      id: "faqs-section",
      label: "FAQs",
      blockTypes: ["ComponentPdpTemplatesGotAQuestionTemplate"],
      order: 2,
    },
    {
      id: "offers-section",
      label: "Offers",
      blockTypes: ["ComponentPdpTemplatesOffersTemplate"],
      order: 3,
    },
    {
      id: "certificates-section",
      label: "Certificates",
      blockTypes: ["ComponentPdpTemplatesCertificateBannerTemplate"],
      order: 4,
    },
    {
      id: "ingredients-section",
      label: "Ingredients",
      blockTypes: [
        "ComponentPdpTemplatesIngredientDetailsTemplate",
        "IngredientTemplate",
      ],
      order: 5,
    },
    {
      id: "sachets-section",
      label: "Sachets",
      blockTypes: [
        "ComponentPdpTemplatesSachetsTemplate",
        "SachetsTemplate",
        "SachetTemplate",
      ],
      order: 6,
    },
  ];

  // Check each section configuration against available blocks
  sectionConfigs.forEach((config) => {
    // Check template.blocks for matching components
    const hasMatchingBlock = productBlocks.some((block: any) => {
      const blockType =
        block.__typename || block.__component || block.type || "";

      // Use exact matching for DynamicTemplate component names (check __typename first)
      const isMatch = config.blockTypes.some((type) => blockType === type);

      return isMatch;
    });

    // Check for direct component rendering (for components not in template.blocks)
    let hasDirectComponent = false;
    if (config.id === "offers-section") {
      hasDirectComponent = directComponentChecks.offers;
    } else if (config.id === "ingredients-section") {
      hasDirectComponent = directComponentChecks.ingredients;
    }

    const shouldIncludeSection = hasMatchingBlock || hasDirectComponent;

    if (shouldIncludeSection) {
      availableSections.push({
        id: config.id,
        label: config.label,
        available: true,
        metadata: { order: config.order },
      });
    }
  });

  // Sort by order
  return availableSections.sort((a, b) => {
    const orderA = a.metadata?.order || 999;
    const orderB = b.metadata?.order || 999;
    return orderA - orderB;
  });
};

/**
 * Utility to add section IDs to DOM elements
 * This should be called when rendering sections to ensure they have the correct IDs
 */
export const ensureSectionIds = (sections: NavigationSection[]) => {
  sections.forEach((section) => {
    // This is a utility function that components can use to ensure
    // their DOM elements have the correct IDs for navigation
    const element = document.getElementById(section.id);
    if (!element) {
      console.warn(`Navigation section element not found: ${section.id}`);
    }
  });
};
