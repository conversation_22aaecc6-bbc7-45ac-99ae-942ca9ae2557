import React, { useState, useMemo } from "react";
import Discount from "@/assets/icons/Discount";
import Tap from "@/assets/icons/Tap";
import Copy from "@/assets/icons/Copy";
import { CopyToast } from "@/components/Common/CopyToast";
import { BestPriceCouponProps } from "../types";

const BestPriceCoupon: React.FC<BestPriceCouponProps> = ({
  productType,
  strapiProduct,
  medusaProduct,
  onCouponClick,
}) => {
  const backgroundColor = strapiProduct?.bg_color || "#ffffff";

  // Calculate current price from the first variant
  const currentPrice = useMemo(() => {
    const activeVariant = medusaProduct?.variants?.[0];
    if (!activeVariant) return 0;

    return (
      activeVariant.extended_product_variants?.compare_at_price ||
      activeVariant.calculated_price?.calculated_amount ||
      0
    );
  }, [medusaProduct?.variants]);

  const [revel, setRevel] = useState(false);

  const handleTapToKnow = () => {
    setRevel(true);
    if (onCouponClick) {
      onCouponClick();
    }
  };

  const handleCopyCode = () => {
    // Simulate copying to clipboard
    navigator.clipboard.writeText("SAVE20");

    CopyToast({
      message: "Coupon code copied!",
      actionText: "Go to cart",
      onAction: () => {
        console.log("Navigating to cart...");
        // Add your navigation logic here
      },
    });
  };

  if (productType === "BYOB") return null;

  return (
    <div className="mb-6" id="offers-section">
      <div className="w-full flex items-center overflow-hidden rounded-[6px]">
        {/* left */}
        <div className="bg-[#FFCF1E] w-[40%] relative rounded-l-[6px]">
          <div className="border-2 border-black pl-5 min-h-12.5 rounded-tl-1.5 rounded-bl-1.5 flex gap-1 items-center justify-start">
            <Discount />
            <div className="flex flex-col">
              <p className="text-[10px] text-black font-obviously leading3.5">
                Get this for
              </p>
              <p className="text-base leading-4 text-right font-obviously font-semibold">
                ₹{currentPrice?.toLocaleString()}
              </p>
            </div>
          </div>

          {/* c1 */}
          <div
            className="h-5 w-5 absolute z-2  border-2 border-black rounded-full top-1/2 left-0 transform -translate-x-1/2 -translate-y-1/2"
            style={{ backgroundColor: backgroundColor }}
          />
        </div>
        {/* right */}
        <div className="relative w-[60%] bg-black min-h-12.5 overflow-hidden rounded-tr-1.5 rounded-br-1.5 flex items-center justify-center gap-2 cursor-pointer">
          {!revel ? (
            <>
              <span
                className="uppercase font-bold pb-1 font-obviously gap-2.5 bg-black border-2 border-black text-[#ffffff]"
                onClick={handleTapToKnow}
                style={{ color: backgroundColor }}
              >
                tap to know
              </span>
              <Tap color={backgroundColor} />
            </>
          ) : (
            <div className="flex items-center gap-2.5 cursor-pointer">
              <div className="flex flex-col text-white">
                <div className="flex gap-1 font-obviously">
                  <span className="text-[10.45px] font-medium">Use code</span>
                  <span className="text-[12.5px] font-extrabold">LIGHTEST</span>
                </div>
                <div
                  style={{ color: backgroundColor }}
                  className="text-[10.45px] font-obviously text-white font-medium leading-[10.45px]"
                >
                  and get extra{""}
                  <span
                    style={{ color: backgroundColor }}
                    className="text-[10.45px] font-obviously text-white font-medium leading-[10.45px]"
                  >
                    {" "}
                    10% off{" "}
                  </span>
                </div>
              </div>
              {/* ICON HERE */}
              <Copy onClick={handleCopyCode} />
            </div>
          )}
          <div
            className="h-5 w-5 absolute z-2  border-2 border-[#000000] rounded-[50%] top-1/2 right-0 translate-x-1/2 -translate-y-1/2"
            style={{ backgroundColor: backgroundColor }}
          />
        </div>
      </div>
    </div>
  );
};

export default BestPriceCoupon;
